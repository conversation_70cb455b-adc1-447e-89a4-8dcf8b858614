/* All Visitors Details Page Styles */
.all-visitors-container {
  min-height: 100vh;

  position: relative;
  overflow: hidden;
  padding: 20px;
}

/* Complex background styling matching dashboard */
.all-visitors-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
  z-index: 1;
}

/* Floating particles animation */
.all-visitors-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(2px 2px at 20px 30px, rgba(255,255,255,0.3), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.2), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.4), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.2), transparent);
  background-repeat: repeat;
  background-size: 150px 100px;
  animation: float 20s infinite linear;
  z-index: 2;
}

@keyframes float {
  0% { transform: translate(0, 0); }
  100% { transform: translate(-150px, -100px); }
}

/* Content positioning */
.all-visitors-container > * {
  position: relative;
  z-index: 3;
}



/* Header */
.all-visitors-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 15px;
}

.all-visitors-header h1 {
  color: #fff;
  font-size: 2.5rem;
  font-weight: 900;
  text-shadow: 0 4px 8px rgba(0,0,0,0.3);
  display: flex;
  align-items: center;
  gap: 15px;
  margin: 0;
}

.back-button {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: #fff;
  padding: 12px 20px;
  border-radius: 12px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
}

.geo-loading {
  color: #fff;
  font-size: 0.9rem;
  opacity: 0.8;
  animation: pulse 2s infinite;
}

/* Controls */
.visitors-controls {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.control-group label {
  color: #fff;
  font-weight: 600;
  font-size: 0.9rem;
}

.control-group select {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: #fff;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 0.9rem;
  backdrop-filter: blur(10px);
}

.control-group select option {
  background: #333;
  color: #fff;
}

.show-all-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: #fff;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.show-all-btn:hover {
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.show-all-btn.active {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.show-all-btn.active:hover {
  background: linear-gradient(135deg, #ee5a24 0%, #ff6b6b 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.5);
}

/* Summary Stats */
.visitors-summary {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.summary-stat {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  padding: 15px 20px;
  border-radius: 12px;
  color: #fff;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
  backdrop-filter: blur(10px);
}

/* Visitors Grid */
.all-visitors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.visitor-card {
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.visitor-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.visitor-card:hover::before {
  opacity: 1;
}

.visitor-card:hover {
  transform: translateY(-5px);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.visitor-card > * {
  position: relative;
  z-index: 2;
}

/* Visitor Card Header */
.visitor-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.visitor-location {
  display: flex;
  align-items: center;
  gap: 12px;
}

.country-flag {
  font-size: 2rem;
  line-height: 1;
  display: inline-block;
  vertical-align: middle;
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", "Android Emoji", "EmojiSymbols", "EmojiOne Mozilla", "Twemoji Mozilla", "Segoe UI Symbol", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

.location-info {
  display: flex;
  flex-direction: column;
}

.country-name {
  color: #fff;
  font-weight: 700;
  font-size: 1.1rem;
}

.city-name {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

.visitor-ip {
  color: rgba(255, 255, 255, 0.8);
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  background: rgba(0, 0, 0, 0.2);
  padding: 4px 8px;
  border-radius: 6px;
}

/* Visitor Stats */
.visitor-stats {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #fff;
  font-size: 0.9rem;
}

.stat-icon {
  color: rgba(255, 255, 255, 0.7);
}

/* Visitor Details */
.visitor-details {
  margin-bottom: 15px;
}

.most-visited {
  color: #fff;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.visit-times {
  display: flex;
  justify-content: space-between;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
}

/* Visitor Card Footer */
.visitor-card-footer {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8rem;
  text-align: center;
  padding-top: 10px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Loading and Error States */
.all-visitors-loading,
.all-visitors-error {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
  color: #fff;
  font-size: 1.2rem;
  text-align: center;
}

.no-visitors {
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.1rem;
  padding: 40px;
}



/* Visitor Chart Section Styles */
.visitor-chart-section {
  margin: 50px 0 30px 0;
  position: relative;
  z-index: 3;
}

.chart-title {
  font-size: 2rem;
  color: #fff;
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  gap: 15px;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  text-align: center;
  justify-content: center;
}

.chart-title svg {
  color: #FF2D55;
  font-size: 1.8rem;
}

.chart-container {
  background: linear-gradient(135deg, rgba(0,0,0,0.8) 0%, rgba(75,0,130,0.1) 100%);
  border-radius: 25px;
  padding: 30px;
  border: 2px solid rgba(255,255,255,0.1);
  backdrop-filter: blur(15px);
  box-shadow:
    0 15px 35px rgba(75,0,130,0.2),
    0 8px 20px rgba(255,45,85,0.15),
    0 3px 10px rgba(0,0,0,0.3);
  margin-bottom: 20px;
  position: relative;
  overflow-x: auto;
  overflow-y: hidden;
  /* Custom scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 45, 85, 0.5) rgba(255, 255, 255, 0.1);
}

/* Webkit scrollbar styling for Chrome/Safari */
.chart-container::-webkit-scrollbar {
  height: 8px;
}

.chart-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

.chart-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #4B0082 0%, #FF2D55 100%);
  border-radius: 10px;
  transition: background 0.3s ease;
}

.chart-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #FF2D55 0%, #4B0082 100%);
}

/* Chart wrapper for horizontal scrolling */
.chart-scroll-wrapper {
  min-width: 800px; /* Minimum width for proper chart display */
  width: 100%;
  min-height: 400px;
  overflow-x: auto;
}

/* Mobile-specific chart improvements */
@media (max-width: 768px) {
  .chart-container {
    padding: 20px 15px;
    /* Show scrollbar on mobile for better UX */
    scrollbar-width: auto;
  }

  .chart-container::-webkit-scrollbar {
    height: 12px;
  }

  .chart-scroll-wrapper {
    min-width: 150%; /* Force wider content on mobile */
  }

  /* Add visual hint for scrolling */
  .chart-container::after {
    content: "← Swipe to scroll →";
    position: absolute;
    bottom: 10px;
    right: 15px;
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
    pointer-events: none;
    animation: fadeInOut 3s infinite;
  }
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.8; }
}

/* Desktop scroll hint */
@media (min-width: 769px) {
  .chart-container::after {
    content: "Use mouse wheel or drag to scroll";
    position: absolute;
    bottom: 10px;
    right: 15px;
    color: rgba(255, 255, 255, 0.4);
    font-size: 11px;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .chart-container:hover::after {
    opacity: 1;
  }
}

.chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #4B0082 0%, #FF2D55 100%);
  border-radius: 25px 25px 0 0;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-top: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #fff;
  font-weight: 500;
  font-size: 1rem;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .all-visitors-container {
    padding: 15px;
  }
  
  .all-visitors-header h1 {
    font-size: 2rem;
  }
  
  .all-visitors-grid {
    grid-template-columns: 1fr;
  }
  
  .visitors-controls {
    flex-direction: column;
  }
  
  .visitors-summary {
    flex-direction: column;
  }
  
  .visitor-card-header {
    flex-direction: column;
    gap: 10px;
  }
  
  .visitor-stats {
    justify-content: space-between;
  }

  .country-flag {
    font-size: 1.8rem;
  }



  .chart-title {
    font-size: 1.6rem;
    flex-direction: column;
    gap: 10px;
  }

  .chart-container {
    padding: 20px;
    margin: 0 -10px 20px -10px;
  }

  .chart-legend {
    flex-direction: column;
    gap: 15px;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .all-visitors-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .visitor-stats {
    flex-direction: column;
    gap: 8px;
  }

  .country-flag {
    font-size: 1.5rem;
  }
}
