// IP Geolocation utility service
// Using multiple services with fallback

import { logDebug, warnDebug, logSensitive } from './logger';

const GEOLOCATION_CACHE = new Map();
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

/**
 * Get country information for an IP address
 * @deprecated Use batchGetCountriesFromIPs for better performance and CORS handling
 * @param {string} ip - IP address to lookup
 * @returns {Promise<Object>} Country information object
 */
export const getCountryFromIP = async (ip) => {
  // Check if IP is localhost or private
  if (ip === '127.0.0.1' || ip === '::1' || ip.startsWith('192.168.') || ip.startsWith('10.') || ip.startsWith('172.')) {
    return {
      country: 'Local',
      country_code: 'LO',
      city: 'Localhost',
      region: 'Local',
      flag: '🏠',
      error: false
    };
  }

  // Check cache first
  const cacheKey = ip;
  const cached = GEOLOCATION_CACHE.get(cacheKey);
  if (cached && (Date.now() - cached.timestamp) < CACHE_DURATION) {
    return cached.data;
  }

  try {
    logDebug(`Getting location for IP: ${ip}`);

    // Create timeout controller
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    // Try ipapi.co first (most reliable)
    const response = await fetch(`https://ipapi.co/${ip}/json/`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Handle API errors
    if (data.error) {
      throw new Error(data.reason || 'API error');
    }

    const countryInfo = {
      country: data.country_name || 'Unknown',
      country_code: data.country_code || 'UN',
      city: data.city || 'Unknown',
      region: data.region || 'Unknown',
      timezone: data.timezone || 'Unknown',
      isp: data.org || 'Unknown',
      flag: getCountryFlag(data.country_code),
      error: false
    };

    // Cache the result
    GEOLOCATION_CACHE.set(cacheKey, {
      data: countryInfo,
      timestamp: Date.now()
    });

    logDebug(`Successfully got location for ${ip}:`, countryInfo);
    return countryInfo;

  } catch (error) {
    warnDebug(`Geolocation lookup failed for IP ${ip}:`, error.message);

    // Return fallback data
    const fallbackData = {
      country: 'Unknown',
      country_code: 'UN',
      city: 'Unknown',
      region: 'Unknown',
      timezone: 'Unknown',
      isp: 'Unknown',
      flag: '🌍',
      error: true,
      errorMessage: error.message
    };

    // Cache the fallback to avoid repeated failed requests
    GEOLOCATION_CACHE.set(cacheKey, {
      data: fallbackData,
      timestamp: Date.now()
    });

    return fallbackData;
  }
};

/**
 * Get country flag emoji from country code
 * @param {string} countryCode - Two-letter country code
 * @returns {string} Flag emoji
 */
const getCountryFlag = (countryCode) => {
  if (!countryCode || countryCode.length !== 2) return '🌍';
  
  const codePoints = countryCode
    .toUpperCase()
    .split('')
    .map(char => 127397 + char.charCodeAt());
  
  return String.fromCodePoint(...codePoints);
};

/**
 * Batch lookup multiple IPs
 * @param {string[]} ips - Array of IP addresses
 * @returns {Promise<Object>} Object with IP as key and country info as value
 */
export const batchGetCountriesFromIPs = async (ips) => {
  logDebug(`Starting batch geolocation lookup for ${ips.length} IPs via backend`);

  // Remove duplicates and filter out empty IPs
  const uniqueIPs = [...new Set(ips.filter(ip => ip && ip.trim() !== ''))];
  logDebug(`Processing ${uniqueIPs.length} unique IPs`);

  if (uniqueIPs.length === 0) {
    logDebug('No valid IPs to process');
    return {};
  }

  try {
    const token = localStorage.getItem('token');
    if (!token) {
      warnDebug('No authentication token found, using direct geolocation');
      throw new Error('No authentication token found');
    }

    const API_URL = process.env.REACT_APP_API_URL;
    if (!API_URL) {
      warnDebug('API URL not configured, using direct geolocation');
      throw new Error('API URL not configured');
    }

    logDebug('Making backend geolocation request...');
    const response = await fetch(`${API_URL}/api/admin/geolocation`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ ips: uniqueIPs })
    });

    if (!response.ok) {
      // If endpoint returns 404, it means the geolocation endpoint is not deployed
      if (response.status === 404) {
        warnDebug('⚠️ Geolocation endpoint not found (404). Using direct API fallback...');
        throw new Error('Endpoint not found - using fallback');
      }
      throw new Error(`Backend geolocation request failed: ${response.status}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.message || 'Backend geolocation lookup failed');
    }

    logDebug(`Backend geolocation completed. Processed ${result.processedCount} IPs`);
    return result.data;

  } catch (error) {
    warnDebug('🔄 Backend geolocation failed, trying direct API calls:', error.message);

    // Enhanced fallback: try direct API calls for each IP
    const fallbackResults = {};

    for (const ip of uniqueIPs) {
      try {
        logDebug(`🌍 Direct lookup for IP: ${ip}`);
        const result = await getCountryFromIP(ip);
        fallbackResults[ip] = result;
        logDebug(`✅ Direct lookup successful for ${ip}: ${result.country}`);
      } catch (directError) {
        warnDebug(`❌ Direct lookup failed for ${ip}:`, directError.message);
        fallbackResults[ip] = {
          country: 'Unknown',
          country_code: 'UN',
          city: 'Unknown',
          region: 'Unknown',
          timezone: 'Unknown',
          isp: 'Unknown',
          flag: '🌍',
          error: true,
          errorMessage: 'All geolocation services unavailable'
        };
      }

      // Add small delay to avoid rate limiting
      if (uniqueIPs.length > 1) {
        await new Promise(resolve => setTimeout(resolve, 200));
      }
    }

    logDebug(`🔄 Fallback completed. Processed ${Object.keys(fallbackResults).length} IPs`);
    return fallbackResults;
  }
};

/**
 * Clear the geolocation cache
 */
export const clearGeolocationCache = () => {
  GEOLOCATION_CACHE.clear();
};

/**
 * Get cache statistics
 * @returns {Object} Cache statistics
 */
export const getCacheStats = () => {
  return {
    size: GEOLOCATION_CACHE.size,
    entries: Array.from(GEOLOCATION_CACHE.keys())
  };
};
