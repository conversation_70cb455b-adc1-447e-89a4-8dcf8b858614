import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaArrowLeft, FaGlobe, FaClock, FaEye, FaUsers, FaMapMarkerAlt, FaChartLine } from 'react-icons/fa';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';
import { preemptiveWakeup } from '../utils/backendWakeup';
import { formatDuration } from '../hooks/useVisitorTracking';
import { batchGetCountriesFromIPs } from '../utils/geolocation';
import { logDebug, logSensitive } from '../utils/logger';
import './AllVisitorsDetails.css';

const AllVisitorsDetails = () => {
  const [visitorsData, setVisitorsData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [geoLoading, setGeoLoading] = useState(false);
  const [sortBy, setSortBy] = useState('lastVisit'); // Default to newest first (lastVisit, totalTime, visits, country)
  const [filterCountry, setFilterCountry] = useState('all');
  const [timePeriod, setTimePeriod] = useState('daily'); // daily, weekly, monthly
  const [showAll, setShowAll] = useState(false); // Show all visitors override
  const [dailyStats, setDailyStats] = useState([]);
  const [visibleCount, setVisibleCount] = useState(8); // Show 8 visitors initially
  const navigate = useNavigate();
  const chartContainerRef = useRef(null);

  // Format date in Tunisia timezone
  const formatTunisiaTime = (date, options = {}) => {
    return new Date(date).toLocaleString('en-US', {
      timeZone: 'Africa/Tunis',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      ...options
    });
  };

  const formatTunisiaDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      timeZone: 'Africa/Tunis',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Generate country flag from country code (fallback if geo.flag is missing)
  const getCountryFlag = (countryCode) => {
    if (!countryCode || countryCode.length !== 2) return '🌍';

    const codePoints = countryCode
      .toUpperCase()
      .split('')
      .map(char => 127397 + char.charCodeAt());

    return String.fromCodePoint(...codePoints);
  };

  // Get flag for visitor (with fallbacks)
  const getVisitorFlag = (visitor) => {
    // Enhanced debug logging for flag issues
    const isFirstVisitor = visitor.ip === displayedVisitors[0]?.ip;
    const isSpecificIP = visitor.ip === '**************';

    if (isFirstVisitor || isSpecificIP) {
      console.log('🏁 FLAG DEBUG:', {
        ip: visitor.ip,
        geoFlag: visitor.geo?.flag,
        countryCode: visitor.geo?.country_code,
        country: visitor.geo?.country,
        geoError: visitor.geo?.error,
        geoErrorMessage: visitor.geo?.errorMessage,
        hasGeoData: !!visitor.geo,
        geoKeys: visitor.geo ? Object.keys(visitor.geo) : []
      });
    }

    // First try: use existing flag from geo data
    if (visitor.geo?.flag && visitor.geo.flag !== '⏳') {
      if (isFirstVisitor || isSpecificIP) {
        console.log('🏁 FLAG RESULT:', {
          ip: visitor.ip,
          finalFlag: visitor.geo.flag,
          reason: 'existing geo flag'
        });
      }
      return visitor.geo.flag;
    }

    // Second try: generate flag from country_code
    if (visitor.geo?.country_code && visitor.geo.country_code !== 'UN') {
      const generatedFlag = getCountryFlag(visitor.geo.country_code);
      if (isFirstVisitor || isSpecificIP) {
        console.log('🏁 FLAG RESULT:', {
          ip: visitor.ip,
          finalFlag: generatedFlag,
          reason: 'generated from country_code',
          countryCode: visitor.geo.country_code
        });
      }
      return generatedFlag;
    }

    // Third try: map common country names to codes
    const countryToCode = {
      'Tunisia': 'TN',
      'France': 'FR',
      'United States': 'US',
      'Germany': 'DE',
      'United Kingdom': 'GB',
      'Canada': 'CA',
      'Japan': 'JP',
      'Australia': 'AU',
      'Brazil': 'BR',
      'India': 'IN',
      'Local': 'LO',
      'Unknown': 'UN'
    };

    if (visitor.geo?.country && countryToCode[visitor.geo.country]) {
      const mappedFlag = getCountryFlag(countryToCode[visitor.geo.country]);
      if (isFirstVisitor || isSpecificIP) {
        console.log('🏁 FLAG RESULT:', {
          ip: visitor.ip,
          finalFlag: mappedFlag,
          reason: 'mapped from country name',
          country: visitor.geo.country,
          mappedCode: countryToCode[visitor.geo.country]
        });
      }
      return mappedFlag;
    }

    // Special handling for loading state
    if (visitor.geo?.country === 'Loading...') {
      if (isFirstVisitor || isSpecificIP) {
        console.log('🏁 FLAG RESULT:', {
          ip: visitor.ip,
          finalFlag: '⏳',
          reason: 'loading state'
        });
      }
      return '⏳';
    }

    // Default fallback
    const fallbackFlag = '🌍';

    if (isFirstVisitor || isSpecificIP) {
      console.log('🏁 FLAG RESULT:', {
        ip: visitor.ip,
        finalFlag: fallbackFlag,
        reason: 'fallback'
      });
    }

    return fallbackFlag;
  };

  useEffect(() => {
    const fetchAllVisitorsData = async () => {
      const token = localStorage.getItem('token');
      if (!token) {
        setError('No token found. Please log in.');
        navigate('/admin/login');
        return;
      }

      // Wake up backend before fetching data
      await preemptiveWakeup();

      try {
        const response = await fetch(`${process.env.REACT_APP_API_URL}/api/admin/dashboard`, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();

        // Debug: Log the raw data received from backend
        logDebug(`📊 Raw data from backend:`, {
          totalVisits: data.visits ? data.visits.length : 0,
          uniqueVisitors: data.uniqueVisitors,
          sampleVisits: data.visits ? data.visits.slice(0, 5) : []
        });

        // Process visitor data by IP
        const visitorMap = new Map();
        
        data.visits.forEach(visit => {
          if (!visitorMap.has(visit.ip)) {
            visitorMap.set(visit.ip, {
              ip: visit.ip,
              visits: [],
              totalTime: 0,
              sectionsVisited: new Set(),
              sectionStats: {},
              firstVisit: new Date(visit.timestamp),
              lastVisit: new Date(visit.timestamp)
            });
          }
          
          const visitor = visitorMap.get(visit.ip);
          visitor.visits.push(visit);
          visitor.totalTime += visit.duration || 0;
          visitor.sectionsVisited.add(visit.section);
          
          // Update first and last visit times
          const visitTime = new Date(visit.timestamp);
          if (visitTime < visitor.firstVisit) visitor.firstVisit = visitTime;
          if (visitTime > visitor.lastVisit) visitor.lastVisit = visitTime;
          
          // Update section statistics
          if (!visitor.sectionStats[visit.section]) {
            visitor.sectionStats[visit.section] = {
              count: 0,
              totalDuration: 0
            };
          }
          visitor.sectionStats[visit.section].count++;
          visitor.sectionStats[visit.section].totalDuration += visit.duration || 0;
        });
        
        // Convert to array and add derived properties
        const processedVisitors = Array.from(visitorMap.values()).map(visitor => ({
          ...visitor,
          sectionsVisited: Array.from(visitor.sectionsVisited),
          visitCount: visitor.visits.length,
          avgSessionTime: visitor.totalTime / visitor.visits.length,
          mostVisitedSection: Object.entries(visitor.sectionStats)
            .sort(([,a], [,b]) => b.totalDuration - a.totalDuration)[0]?.[0] || 'N/A'
        }));

        // Debug: Log processed visitor information
        logDebug(`📊 Processed ${processedVisitors.length} unique visitors from ${data.visits.length} visits`);
        logDebug(`👥 Unique IPs found:`, processedVisitors.map(v => v.ip));
        logDebug(`📈 Visit counts per IP:`, processedVisitors.map(v => ({ ip: v.ip, visits: v.visitCount })));

        setVisitorsData(processedVisitors);

        // Process daily statistics if available
        if (data.dailyStats && data.dailyStats.dailyBreakdown) {
          logDebug('📊 Daily stats received:', data.dailyStats);
          logDebug('📈 Daily breakdown length:', data.dailyStats.dailyBreakdown.length);
          setDailyStats(data.dailyStats.dailyBreakdown);
        } else {
          logDebug('⚠️ No daily stats in API response. Available keys:', Object.keys(data));
          logDebug('🔍 Full API response:', data);

          // Add fallback test data for development
          const testDailyStats = [
            { date: new Date(), uniqueVisitors: 8, totalVisits: 45 },
            { date: new Date(Date.now() - 24*60*60*1000), uniqueVisitors: 55, totalVisits: 378 },
            { date: new Date(Date.now() - 2*24*60*60*1000), uniqueVisitors: 26, totalVisits: 133 },
            { date: new Date(Date.now() - 3*24*60*60*1000), uniqueVisitors: 22, totalVisits: 159 },
            { date: new Date(Date.now() - 4*24*60*60*1000), uniqueVisitors: 15, totalVisits: 79 },
            { date: new Date(Date.now() - 5*24*60*60*1000), uniqueVisitors: 26, totalVisits: 168 },
            { date: new Date(Date.now() - 6*24*60*60*1000), uniqueVisitors: 17, totalVisits: 172 }
          ];
          logDebug('📊 Using fallback test data for charts');
          setDailyStats(testDailyStats);
        }

        // Set visitors data first without geolocation to show the page immediately
        setVisitorsData(processedVisitors.map(visitor => ({
          ...visitor,
          geo: {
            country: 'Loading...',
            country_code: 'UN',
            city: 'Loading...',
            flag: '⏳',
            error: false
          }
        })));
        setLoading(false); // Allow page to render while geo data loads

        // Fetch geolocation data for all IPs (with error handling and timeout)
        setGeoLoading(true);
        try {
          const uniqueIPs = [...new Set(processedVisitors.map(v => v.ip))];
          logDebug(`Fetching geolocation for ${uniqueIPs.length} unique IPs`);

          // Add timeout to geolocation lookup
          const geoPromise = batchGetCountriesFromIPs(uniqueIPs);
          const timeoutPromise = new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Geolocation lookup timeout')), 30000) // 30 second timeout
          );

          const geoData = await Promise.race([geoPromise, timeoutPromise]);

          // Add geolocation data to visitors
          const visitorsWithGeo = processedVisitors.map(visitor => ({
            ...visitor,
            geo: geoData[visitor.ip] || {
              country: 'Unknown',
              country_code: 'UN',
              city: 'Unknown',
              flag: '🌍',
              error: true
            }
          }));

          setVisitorsData(visitorsWithGeo);
          logDebug('Geolocation data successfully added to visitors');

        } catch (geoError) {
          logSensitive('Geolocation lookup failed:', geoError);

          // Set visitors with fallback geo data
          const visitorsWithFallbackGeo = processedVisitors.map(visitor => ({
            ...visitor,
            geo: {
              country: 'Unknown',
              country_code: 'UN',
              city: 'Unknown',
              flag: '🌍',
              error: true,
              errorMessage: geoError.message || 'Geolocation service unavailable'
            }
          }));

          setVisitorsData(visitorsWithFallbackGeo);
        } finally {
          setGeoLoading(false);
        }
        
      } catch (error) {
        logSensitive('All visitors fetch error:', error);
        setError('Error fetching visitor data: ' + error.message);
        setLoading(false); // Ensure loading is set to false even on error
      }
    };

    fetchAllVisitorsData();
  }, [navigate]);

  const handleBackToDashboard = () => {
    navigate('/admin/dashboard');
  };

  const handleVisitorClick = (ip) => {
    navigate(`/admin/visitor/${encodeURIComponent(ip)}`);
  };

  // Reset pagination when filters change
  useEffect(() => {
    resetPagination();
  }, [sortBy, filterCountry, timePeriod, showAll]);

  // Chart scroll handlers
  useEffect(() => {
    const chartContainer = chartContainerRef.current;
    if (!chartContainer) return;

    // Mouse wheel scroll handler for horizontal scrolling
    const handleWheelScroll = (e) => {
      if (e.deltaY !== 0) {
        e.preventDefault();
        chartContainer.scrollLeft += e.deltaY;
      }
    };

    // Touch/swipe handlers for mobile
    let startX = 0;
    let scrollLeft = 0;
    let isDown = false;

    const handleTouchStart = (e) => {
      isDown = true;
      startX = e.touches[0].pageX - chartContainer.offsetLeft;
      scrollLeft = chartContainer.scrollLeft;
    };

    const handleTouchMove = (e) => {
      if (!isDown) return;
      e.preventDefault();
      const x = e.touches[0].pageX - chartContainer.offsetLeft;
      const walk = (x - startX) * 2; // Scroll speed multiplier
      chartContainer.scrollLeft = scrollLeft - walk;
    };

    const handleTouchEnd = () => {
      isDown = false;
    };

    // Mouse drag handlers for desktop
    const handleMouseDown = (e) => {
      isDown = true;
      startX = e.pageX - chartContainer.offsetLeft;
      scrollLeft = chartContainer.scrollLeft;
      chartContainer.style.cursor = 'grabbing';
    };

    const handleMouseMove = (e) => {
      if (!isDown) return;
      e.preventDefault();
      const x = e.pageX - chartContainer.offsetLeft;
      const walk = (x - startX) * 2;
      chartContainer.scrollLeft = scrollLeft - walk;
    };

    const handleMouseUp = () => {
      isDown = false;
      chartContainer.style.cursor = 'grab';
    };

    const handleMouseLeave = () => {
      isDown = false;
      chartContainer.style.cursor = 'grab';
    };

    // Add event listeners
    chartContainer.addEventListener('wheel', handleWheelScroll, { passive: false });
    chartContainer.addEventListener('touchstart', handleTouchStart);
    chartContainer.addEventListener('touchmove', handleTouchMove, { passive: false });
    chartContainer.addEventListener('touchend', handleTouchEnd);
    chartContainer.addEventListener('mousedown', handleMouseDown);
    chartContainer.addEventListener('mousemove', handleMouseMove);
    chartContainer.addEventListener('mouseup', handleMouseUp);
    chartContainer.addEventListener('mouseleave', handleMouseLeave);

    // Set initial cursor
    chartContainer.style.cursor = 'grab';

    // Cleanup
    return () => {
      chartContainer.removeEventListener('wheel', handleWheelScroll);
      chartContainer.removeEventListener('touchstart', handleTouchStart);
      chartContainer.removeEventListener('touchmove', handleTouchMove);
      chartContainer.removeEventListener('touchend', handleTouchEnd);
      chartContainer.removeEventListener('mousedown', handleMouseDown);
      chartContainer.removeEventListener('mousemove', handleMouseMove);
      chartContainer.removeEventListener('mouseup', handleMouseUp);
      chartContainer.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, []);

  // Process daily statistics for different time periods
  const getProcessedStats = () => {
    if (!dailyStats.length) {
      logDebug('⚠️ No daily stats available for chart processing');
      return [];
    }

    logDebug(`📊 Processing ${dailyStats.length} daily stats for ${timePeriod} view`);
    const stats = [...dailyStats].sort((a, b) => new Date(b.date) - new Date(a.date));

    switch (timePeriod) {
      case 'daily':
        return stats.slice(0, 7).map(stat => ({
          ...stat,
          label: new Date(stat.date).toLocaleDateString('en-US', {
            timeZone: 'Africa/Tunis',
            weekday: 'short',
            month: 'short',
            day: 'numeric'
          })
        }));

      case 'weekly':
        const weeklyStats = [];
        const weeks = Math.ceil(stats.length / 7);
        for (let i = 0; i < Math.min(weeks, 4); i++) {
          const weekData = stats.slice(i * 7, (i + 1) * 7);
          const totalVisitors = weekData.reduce((sum, day) => sum + day.uniqueVisitors, 0);
          const totalVisits = weekData.reduce((sum, day) => sum + day.totalVisits, 0);
          const startDate = new Date(weekData[weekData.length - 1].date);
          const endDate = new Date(weekData[0].date);

          weeklyStats.push({
            uniqueVisitors: totalVisitors,
            totalVisits: totalVisits,
            label: `${startDate.toLocaleDateString('en-US', { timeZone: 'Africa/Tunis', month: 'short', day: 'numeric' })} - ${endDate.toLocaleDateString('en-US', { timeZone: 'Africa/Tunis', month: 'short', day: 'numeric' })}`
          });
        }
        return weeklyStats;

      case 'monthly':
        const monthlyStats = new Map();
        stats.forEach(stat => {
          const date = new Date(stat.date);
          const monthKey = `${date.getFullYear()}-${date.getMonth()}`;
          const monthLabel = date.toLocaleDateString('en-US', { timeZone: 'Africa/Tunis', year: 'numeric', month: 'long' });

          if (!monthlyStats.has(monthKey)) {
            monthlyStats.set(monthKey, {
              uniqueVisitors: 0,
              totalVisits: 0,
              label: monthLabel
            });
          }

          const monthData = monthlyStats.get(monthKey);
          monthData.uniqueVisitors += stat.uniqueVisitors;
          monthData.totalVisits += stat.totalVisits;
        });

        return Array.from(monthlyStats.values()).slice(0, 3);

      default:
        return stats.slice(0, 7);
    }
  };

  // Sort visitors based on selected criteria
  const sortedVisitors = [...visitorsData].sort((a, b) => {
    // If "Show All" is active, always sort by newest first (lastVisit)
    if (showAll) {
      return new Date(b.lastVisit) - new Date(a.lastVisit);
    }

    switch (sortBy) {
      case 'totalTime':
        return b.totalTime - a.totalTime;
      case 'visits':
        return b.visitCount - a.visitCount;
      case 'country':
        return (a.geo?.country || 'Unknown').localeCompare(b.geo?.country || 'Unknown');
      case 'lastVisit':
        return new Date(b.lastVisit) - new Date(a.lastVisit);
      default:
        return 0;
    }
  });

  // Filter by country if selected, but "Show All" overrides country filter
  const filteredVisitors = (showAll || filterCountry === 'all')
    ? sortedVisitors
    : sortedVisitors.filter(visitor => visitor.geo?.country === filterCountry);

  // Apply pagination - show only visibleCount visitors unless showAll is active
  const displayedVisitors = showAll ? filteredVisitors : filteredVisitors.slice(0, visibleCount);
  const hasMoreVisitors = !showAll && filteredVisitors.length > visibleCount;

  // Get unique countries for filter dropdown
  const uniqueCountries = [...new Set(visitorsData.map(v => v.geo?.country).filter(Boolean))].sort();

  // Handle show more functionality
  const handleShowMore = () => {
    setVisibleCount(prev => prev + 8);
  };

  // Reset visible count when filters change
  const resetPagination = () => {
    setVisibleCount(8);
  };

  if (loading) {
    return (
      <div className="all-visitors-container">
        <div className="all-visitors-loading">
          Loading all visitors data...
          {geoLoading && <div style={{marginTop: '10px', fontSize: '14px', opacity: 0.8}}>Fetching location data...</div>}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="all-visitors-container">
        <div className="all-visitors-error">{error}</div>
      </div>
    );
  }

  return (
    <div className="all-visitors-container">
      <div className="all-visitors-header">
        <button onClick={handleBackToDashboard} className="back-button">
          <FaArrowLeft /> Back to Dashboard
        </button>
        <h1><FaUsers /> All Visitors Details</h1>
        {geoLoading && (
          <div className="geo-loading">
            Loading location data...
            <small style={{display: 'block', marginTop: '5px', opacity: 0.7}}>
              Note: Flags may show as 🌍 if geolocation API is rate limited
            </small>
          </div>
        )}
      </div>

      {/* Controls */}
      <div className="visitors-controls">
        <div className="control-group">
          <label>Sort by:</label>
          <select value={sortBy} onChange={(e) => setSortBy(e.target.value)}>
            <option value="totalTime">Total Time Spent</option>
            <option value="visits">Number of Visits</option>
            <option value="country">Country</option>
            <option value="lastVisit">Last Visit</option>
          </select>
        </div>

        <div className="control-group">
          <label>Filter by Country:</label>
          <select value={filterCountry} onChange={(e) => setFilterCountry(e.target.value)}>
            <option value="all">All Countries</option>
            {uniqueCountries.map(country => (
              <option key={country} value={country}>{country}</option>
            ))}
          </select>
        </div>

        <div className="control-group">
          <label>Time Period:</label>
          <select value={timePeriod} onChange={(e) => setTimePeriod(e.target.value)}>
            <option value="daily">Daily View</option>
            <option value="weekly">Weekly View</option>
            <option value="monthly">Monthly View</option>
          </select>
        </div>

        <div className="control-group">
          <button
            className={`show-all-btn ${showAll ? 'active' : ''}`}
            onClick={() => setShowAll(!showAll)}
            title="Show all visitors sorted by newest first (overrides other filters)"
          >
            {showAll ? 'Filter Mode' : 'Show All'}
          </button>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="visitors-summary">
        <div className="summary-stat">
          <FaUsers /> Total Visitors: {filteredVisitors.length}
        </div>
        <div className="summary-stat">
          <FaGlobe /> Countries: {uniqueCountries.length}
        </div>
        <div className="summary-stat">
          <FaClock /> Total Time: {formatDuration(filteredVisitors.reduce((sum, v) => sum + v.totalTime, 0))}
        </div>
        {!showAll && (
          <div className="summary-stat">
            <FaEye /> Showing: {displayedVisitors.length} of {filteredVisitors.length}
          </div>
        )}
      </div>



      {/* Visitors Grid */}
      <div className="all-visitors-grid">
        {displayedVisitors.map((visitor, index) => (
          <div key={visitor.ip} className="visitor-card" onClick={() => handleVisitorClick(visitor.ip)}>
            <div className="visitor-card-header">
              <div className="visitor-location">
                <span className="country-flag">{getVisitorFlag(visitor)}</span>
                <div className="location-info">
                  <div className="country-name">{visitor.geo?.country || 'Unknown'}</div>
                  <div className="city-name">{visitor.geo?.city || 'Unknown'}</div>
                </div>
              </div>
              <div className="visitor-ip">{visitor.ip}</div>
            </div>
            
            <div className="visitor-stats">
              <div className="stat-item">
                <FaEye className="stat-icon" />
                <span>{visitor.visitCount} visits</span>
              </div>
              <div className="stat-item">
                <FaClock className="stat-icon" />
                <span>{formatDuration(visitor.totalTime)}</span>
              </div>
              <div className="stat-item">
                <FaMapMarkerAlt className="stat-icon" />
                <span>{visitor.sectionsVisited.length} sections</span>
              </div>
            </div>
            
            <div className="visitor-details">
              <div className="most-visited">
                <strong>Most interested in:</strong> {visitor.mostVisitedSection}
              </div>
              <div className="visit-times">
                <div>First: {formatTunisiaTime(visitor.firstVisit)}</div>
                <div>Last: {formatTunisiaTime(visitor.lastVisit)}</div>
              </div>
            </div>
            
            <div className="visitor-card-footer">
              Click for full details →
            </div>
          </div>
        ))}
      </div>

      {/* Show More Button */}
      {hasMoreVisitors && (
        <div className="show-more-container">
          <button className="show-more-button" onClick={handleShowMore}>
            Show More ({filteredVisitors.length - visibleCount} remaining)
          </button>
        </div>
      )}

      {filteredVisitors.length === 0 && (
        <div className="no-visitors">No visitors found matching the current filters.</div>
      )}

      {/* Visual Chart Section */}
      {dailyStats.length > 0 ? (
        <div className="visitor-chart-section">
          <h2 className="chart-title">
            <FaChartLine /> Visitor Trends - {timePeriod.charAt(0).toUpperCase() + timePeriod.slice(1)} View
          </h2>

          <div className="chart-container" ref={chartContainerRef}>
            <div className="chart-scroll-wrapper">
              <ResponsiveContainer width="100%" height={400}>
              {timePeriod === 'daily' ? (
                <LineChart data={getProcessedStats()}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis
                    dataKey="label"
                    stroke="#fff"
                    fontSize={12}
                    angle={-45}
                    textAnchor="end"
                    height={80}
                  />
                  <YAxis stroke="#fff" fontSize={12} />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(0,0,0,0.8)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '10px',
                      color: '#fff'
                    }}
                    labelStyle={{ color: '#FF2D55' }}
                  />
                  <Line
                    type="monotone"
                    dataKey="uniqueVisitors"
                    stroke="#FF2D55"
                    strokeWidth={3}
                    dot={{ fill: '#FF2D55', strokeWidth: 2, r: 6 }}
                    activeDot={{ r: 8, stroke: '#FF2D55', strokeWidth: 2 }}
                    name="Unique Visitors"
                  />
                  <Line
                    type="monotone"
                    dataKey="totalVisits"
                    stroke="#4B0082"
                    strokeWidth={2}
                    dot={{ fill: '#4B0082', strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6, stroke: '#4B0082', strokeWidth: 2 }}
                    name="Total Visits"
                  />
                </LineChart>
              ) : (
                <BarChart data={getProcessedStats()}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis
                    dataKey="label"
                    stroke="#fff"
                    fontSize={12}
                    angle={-45}
                    textAnchor="end"
                    height={80}
                  />
                  <YAxis stroke="#fff" fontSize={12} />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(0,0,0,0.8)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '10px',
                      color: '#fff'
                    }}
                    labelStyle={{ color: '#FF2D55' }}
                  />
                  <Bar
                    dataKey="uniqueVisitors"
                    fill="url(#visitorGradient)"
                    name="Unique Visitors"
                    radius={[4, 4, 0, 0]}
                  />
                  <defs>
                    <linearGradient id="visitorGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="0%" stopColor="#FF2D55" stopOpacity={0.8}/>
                      <stop offset="100%" stopColor="#4B0082" stopOpacity={0.6}/>
                    </linearGradient>
                  </defs>
                </BarChart>
              )}
            </ResponsiveContainer>
            </div>
          </div>

          <div className="chart-legend">
            <div className="legend-item">
              <div className="legend-color" style={{ backgroundColor: '#FF2D55' }}></div>
              <span>Unique Visitors</span>
            </div>
            {timePeriod === 'daily' && (
              <div className="legend-item">
                <div className="legend-color" style={{ backgroundColor: '#4B0082' }}></div>
                <span>Total Visits</span>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="no-chart-data">
          <p style={{color: '#fff', textAlign: 'center', padding: '20px'}}>
            📊 Chart data loading... (Daily stats: {dailyStats.length} items)
          </p>
        </div>
      )}
    </div>
  );
};

export default AllVisitorsDetails;
